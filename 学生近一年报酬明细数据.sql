-- 学生近一年报酬明细数据 (SYT_QGZX_REMUNERATION_DETAIL)
-- 时间范围：2024年8月 - 2025年7月
-- 学生：王志伟(202500001), 蒋欣谣(202500002), 王思帆(202500003), 姚卓成(202500004), 卿建国(202500006)

-- ========================================
-- 报酬明细数据 (SYT_QGZX_REMUNERATION_DETAIL)
-- ========================================

-- 王志伟 (202500001) - 图书馆助理明细
INSERT INTO SYT_QGZX_REMUNERATION_DETAIL (ID, REMUNERATION_APPLY_ID, STUDENT_APPLY_ID, XGH, HOURS_SOURCE_TYPE, WORK_HOURS, HOURLY_RATE, REMUNERATION_AMOUNT, REMARK, CREATE_TIME, UPDATE_TIME) VALUES 
('wzw202408001_detail', 'wzw202408001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 45.5, 15.00, 682.50, '基于考勤记录自动计算', TIMESTAMP '2024-08-25 15:30:10', null),
('wzw202409001_detail', 'wzw202409001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 52.0, 15.00, 780.00, '基于考勤记录自动计算', TIMESTAMP '2024-09-25 16:20:10', null),
('wzw202410001_detail', 'wzw202410001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 48.0, 15.00, 720.00, '基于考勤记录自动计算', TIMESTAMP '2024-10-25 14:45:10', null),
('wzw202411001_detail', 'wzw202411001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 44.5, 15.00, 667.50, '基于考勤记录自动计算', TIMESTAMP '2024-11-25 15:10:10', null),
('wzw202412001_detail', 'wzw202412001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 50.0, 15.00, 750.00, '基于考勤记录自动计算', TIMESTAMP '2024-12-25 16:30:10', null),
('wzw202501001_detail', 'wzw202501001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 42.0, 15.00, 630.00, '基于考勤记录自动计算', TIMESTAMP '2025-01-25 15:45:10', null),
('wzw202502001_detail', 'wzw202502001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 38.5, 15.00, 577.50, '基于考勤记录自动计算', TIMESTAMP '2025-02-25 14:20:10', null),
('wzw202503001_detail', 'wzw202503001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 46.0, 15.00, 690.00, '基于考勤记录自动计算', TIMESTAMP '2025-03-25 16:15:10', null),
('wzw202504001_detail', 'wzw202504001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 49.5, 15.00, 742.50, '基于考勤记录自动计算', TIMESTAMP '2025-04-25 15:25:10', null),
('wzw202505001_detail', 'wzw202505001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 51.0, 15.00, 765.00, '基于考勤记录自动计算', TIMESTAMP '2025-05-25 14:50:10', null),
('wzw202506001_detail', 'wzw202506001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 47.5, 15.00, 712.50, '基于考勤记录自动计算', TIMESTAMP '2025-06-25 16:40:10', null),
('wzw202507001_detail', 'wzw202507001', '1099dcc2a59af999746966a493def441', '202500001', 'AUTO_ATTENDANCE', 53.0, 15.00, 795.00, '基于考勤记录自动计算', TIMESTAMP '2025-07-25 15:55:10', null);

-- 蒋欣谣 (202500002) - 实验室助理明细
INSERT INTO SYT_QGZX_REMUNERATION_DETAIL (ID, REMUNERATION_APPLY_ID, STUDENT_APPLY_ID, XGH, HOURS_SOURCE_TYPE, WORK_HOURS, HOURLY_RATE, REMUNERATION_AMOUNT, REMARK, CREATE_TIME, UPDATE_TIME) VALUES 
('jxy202408001_detail', 'jxy202408001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 40.0, 18.00, 720.00, '基于考勤记录自动计算', TIMESTAMP '2024-08-26 10:30:10', null),
('jxy202409001_detail', 'jxy202409001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 44.5, 18.00, 801.00, '基于考勤记录自动计算', TIMESTAMP '2024-09-26 11:20:10', null),
('jxy202410001_detail', 'jxy202410001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 42.0, 18.00, 756.00, '基于考勤记录自动计算', TIMESTAMP '2024-10-26 09:45:10', null),
('jxy202411001_detail', 'jxy202411001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 38.5, 18.00, 693.00, '基于考勤记录自动计算', TIMESTAMP '2024-11-26 10:10:10', null),
('jxy202412001_detail', 'jxy202412001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 46.0, 18.00, 828.00, '基于考勤记录自动计算', TIMESTAMP '2024-12-26 11:30:10', null),
('jxy202501001_detail', 'jxy202501001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 39.0, 18.00, 702.00, '基于考勤记录自动计算', TIMESTAMP '2025-01-26 10:45:10', null),
('jxy202502001_detail', 'jxy202502001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 35.5, 18.00, 639.00, '基于考勤记录自动计算', TIMESTAMP '2025-02-26 09:20:10', null),
('jxy202503001_detail', 'jxy202503001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 43.0, 18.00, 774.00, '基于考勤记录自动计算', TIMESTAMP '2025-03-26 11:15:10', null),
('jxy202504001_detail', 'jxy202504001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 41.5, 18.00, 747.00, '基于考勤记录自动计算', TIMESTAMP '2025-04-26 10:25:10', null),
('jxy202505001_detail', 'jxy202505001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 45.0, 18.00, 810.00, '基于考勤记录自动计算', TIMESTAMP '2025-05-26 09:50:10', null),
('jxy202506001_detail', 'jxy202506001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 47.5, 18.00, 855.00, '基于考勤记录自动计算', TIMESTAMP '2025-06-26 11:40:10', null),
('jxy202507001_detail', 'jxy202507001', '2099dcc2a59af999746966a493def442', '202500002', 'AUTO_ATTENDANCE', 49.0, 18.00, 882.00, '基于考勤记录自动计算', TIMESTAMP '2025-07-26 10:55:10', null);

-- 王思帆 (202500003) - 办公室助理明细
INSERT INTO SYT_QGZX_REMUNERATION_DETAIL (ID, REMUNERATION_APPLY_ID, STUDENT_APPLY_ID, XGH, HOURS_SOURCE_TYPE, WORK_HOURS, HOURLY_RATE, REMUNERATION_AMOUNT, REMARK, CREATE_TIME, UPDATE_TIME) VALUES 
('wsf202408001_detail', 'wsf202408001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 36.0, 17.00, 612.00, '基于考勤记录自动计算', TIMESTAMP '2024-08-27 14:30:10', null),
('wsf202409001_detail', 'wsf202409001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 41.5, 17.00, 705.50, '基于考勤记录自动计算', TIMESTAMP '2024-09-27 15:20:10', null),
('wsf202410001_detail', 'wsf202410001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 38.5, 17.00, 654.50, '基于考勤记录自动计算', TIMESTAMP '2024-10-27 13:45:10', null),
('wsf202411001_detail', 'wsf202411001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 35.0, 17.00, 595.00, '基于考勤记录自动计算', TIMESTAMP '2024-11-27 14:10:10', null),
('wsf202412001_detail', 'wsf202412001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 43.0, 17.00, 731.00, '基于考勤记录自动计算', TIMESTAMP '2024-12-27 15:30:10', null),
('wsf202501001_detail', 'wsf202501001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 37.5, 17.00, 637.50, '基于考勤记录自动计算', TIMESTAMP '2025-01-27 14:45:10', null),
('wsf202502001_detail', 'wsf202502001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 32.0, 17.00, 544.00, '基于考勤记录自动计算', TIMESTAMP '2025-02-27 13:20:10', null),
('wsf202503001_detail', 'wsf202503001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 40.0, 17.00, 680.00, '基于考勤记录自动计算', TIMESTAMP '2025-03-27 15:15:10', null),
('wsf202504001_detail', 'wsf202504001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 39.5, 17.00, 671.50, '基于考勤记录自动计算', TIMESTAMP '2025-04-27 14:25:10', null),
('wsf202505001_detail', 'wsf202505001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 42.0, 17.00, 714.00, '基于考勤记录自动计算', TIMESTAMP '2025-05-27 13:50:10', null),
('wsf202506001_detail', 'wsf202506001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 44.5, 17.00, 756.50, '基于考勤记录自动计算', TIMESTAMP '2025-06-27 15:40:10', null),
('wsf202507001_detail', 'wsf202507001', '3099dcc2a59af999746966a493def443', '202500003', 'AUTO_ATTENDANCE', 46.0, 17.00, 782.00, '基于考勤记录自动计算', TIMESTAMP '2025-07-27 14:55:10', null);

-- 姚卓成 (202500004) - 学生会助理明细
INSERT INTO SYT_QGZX_REMUNERATION_DETAIL (ID, REMUNERATION_APPLY_ID, STUDENT_APPLY_ID, XGH, HOURS_SOURCE_TYPE, WORK_HOURS, HOURLY_RATE, REMUNERATION_AMOUNT, REMARK, CREATE_TIME, UPDATE_TIME) VALUES
('yzc202408001_detail', 'yzc202408001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 48.0, 20.00, 960.00, '基于考勤记录自动计算', TIMESTAMP '2024-08-28 16:30:10', null),
('yzc202409001_detail', 'yzc202409001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 52.5, 20.00, 1050.00, '基于考勤记录自动计算', TIMESTAMP '2024-09-28 17:20:10', null),
('yzc202410001_detail', 'yzc202410001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 45.0, 20.00, 900.00, '基于考勤记录自动计算', TIMESTAMP '2024-10-28 15:45:10', null),
('yzc202411001_detail', 'yzc202411001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 41.5, 20.00, 830.00, '基于考勤记录自动计算', TIMESTAMP '2024-11-28 16:10:10', null),
('yzc202412001_detail', 'yzc202412001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 50.0, 20.00, 1000.00, '基于考勤记录自动计算', TIMESTAMP '2024-12-28 17:30:10', null),
('yzc202501001_detail', 'yzc202501001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 44.0, 20.00, 880.00, '基于考勤记录自动计算', TIMESTAMP '2025-01-28 16:45:10', null),
('yzc202502001_detail', 'yzc202502001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 38.5, 20.00, 770.00, '基于考勤记录自动计算', TIMESTAMP '2025-02-28 15:20:10', null),
('yzc202503001_detail', 'yzc202503001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 47.0, 20.00, 940.00, '基于考勤记录自动计算', TIMESTAMP '2025-03-28 17:15:10', null),
('yzc202504001_detail', 'yzc202504001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 49.5, 20.00, 990.00, '基于考勤记录自动计算', TIMESTAMP '2025-04-28 16:25:10', null),
('yzc202505001_detail', 'yzc202505001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 51.0, 20.00, 1020.00, '基于考勤记录自动计算', TIMESTAMP '2025-05-28 15:50:10', null),
('yzc202506001_detail', 'yzc202506001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 53.5, 20.00, 1070.00, '基于考勤记录自动计算', TIMESTAMP '2025-06-28 17:40:10', null),
('yzc202507001_detail', 'yzc202507001', '4099dcc2a59af999746966a493def444', '202500004', 'AUTO_ATTENDANCE', 55.0, 20.00, 1100.00, '基于考勤记录自动计算', TIMESTAMP '2025-07-28 16:55:10', null);

-- 卿建国 (202500006) - 后勤助理明细
INSERT INTO SYT_QGZX_REMUNERATION_DETAIL (ID, REMUNERATION_APPLY_ID, STUDENT_APPLY_ID, XGH, HOURS_SOURCE_TYPE, WORK_HOURS, HOURLY_RATE, REMUNERATION_AMOUNT, REMARK, CREATE_TIME, UPDATE_TIME) VALUES
('qjg202408001_detail', 'qjg202408001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 42.0, 17.00, 714.00, '基于考勤记录自动计算', TIMESTAMP '2024-08-29 08:30:10', null),
('qjg202409001_detail', 'qjg202409001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 46.5, 17.00, 790.50, '基于考勤记录自动计算', TIMESTAMP '2024-09-29 09:20:10', null),
('qjg202410001_detail', 'qjg202410001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 40.0, 17.00, 680.00, '基于考勤记录自动计算', TIMESTAMP '2024-10-29 07:45:10', null),
('qjg202411001_detail', 'qjg202411001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 37.5, 17.00, 637.50, '基于考勤记录自动计算', TIMESTAMP '2024-11-29 08:10:10', null),
('qjg202412001_detail', 'qjg202412001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 44.0, 17.00, 748.00, '基于考勤记录自动计算', TIMESTAMP '2024-12-29 09:30:10', null),
('qjg202501001_detail', 'qjg202501001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 41.0, 17.00, 697.00, '基于考勤记录自动计算', TIMESTAMP '2025-01-29 08:45:10', null),
('qjg202502001_detail', 'qjg202502001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 34.5, 17.00, 586.50, '基于考勤记录自动计算', TIMESTAMP '2025-02-28 07:20:10', null),
('qjg202503001_detail', 'qjg202503001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 43.5, 17.00, 739.50, '基于考勤记录自动计算', TIMESTAMP '2025-03-29 09:15:10', null),
('qjg202504001_detail', 'qjg202504001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 45.5, 17.00, 773.50, '基于考勤记录自动计算', TIMESTAMP '2025-04-29 08:25:10', null),
('qjg202505001_detail', 'qjg202505001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 48.0, 17.00, 816.00, '基于考勤记录自动计算', TIMESTAMP '2025-05-29 07:50:10', null),
('qjg202506001_detail', 'qjg202506001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 50.5, 17.00, 858.50, '基于考勤记录自动计算', TIMESTAMP '2025-06-29 09:40:10', null),
('qjg202507001_detail', 'qjg202507001', '5099dcc2a59af999746966a493def445', '202500006', 'AUTO_ATTENDANCE', 52.0, 17.00, 884.00, '基于考勤记录自动计算', TIMESTAMP '2025-07-29 08:55:10', null);
