-- 2023000914
INSERT INTO SYT_QGZX_REMUNERATION_APPLY (ID, JOB_ID, XNXQ, SBNY, START_DATE, END_DATE, SPZT, WORKFLOW_ID, SQSJ, CREATE_TIME, UPDATE_TIME, SBSM, TOTAL_HOURS, TOTAL_AMOUNT, XGH, ROLE_ID, XXMC, SFTS) VALUES 
('wzw202408001', '84be3e3789866b57e024851de83f7e62', '2024', '2024-08', TIMESTAMP '2024-08-01 12:00:00', TIMESTAMP '2024-08-31 12:00:00', 'TongGuo', '', TIMESTAMP '2024-08-25 15:30:00', TIMESTAMP '2024-08-25 15:30:00', null, '基于考勤记录自动生成', 45.5, 682.50, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202409001', '84be3e3789866b57e024851de83f7e62', '2024', '2024-09', TIMESTAMP '2024-09-01 12:00:00', TIMESTAMP '2024-09-30 12:00:00', 'TongGuo', '', TIMESTAMP '2024-09-25 16:20:00', TIMESTAMP '2024-09-25 16:20:00', null, '基于考勤记录自动生成', 52.0, 780.00, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202410001', '84be3e3789866b57e024851de83f7e62', '2024', '2024-10', TIMESTAMP '2024-10-01 12:00:00', TIMESTAMP '2024-10-31 12:00:00', 'TongGuo', '', TIMESTAMP '2024-10-25 14:45:00', TIMESTAMP '2024-10-25 14:45:00', null, '基于考勤记录自动生成', 48.0, 720.00, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202411001', '84be3e3789866b57e024851de83f7e62', '2024', '2024-11', TIMESTAMP '2024-11-01 12:00:00', TIMESTAMP '2024-11-30 12:00:00', 'TongGuo', '', TIMESTAMP '2024-11-25 15:10:00', TIMESTAMP '2024-11-25 15:10:00', null, '基于考勤记录自动生成', 44.5, 667.50, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202412001', '84be3e3789866b57e024851de83f7e62', '2024', '2024-12', TIMESTAMP '2024-12-01 12:00:00', TIMESTAMP '2024-12-31 12:00:00', 'TongGuo', '', TIMESTAMP '2024-12-25 16:30:00', TIMESTAMP '2024-12-25 16:30:00', null, '基于考勤记录自动生成', 50.0, 750.00, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202501001', '84be3e3789866b57e024851de83f7e62', '2025', '2025-01', TIMESTAMP '2025-01-01 12:00:00', TIMESTAMP '2025-01-31 12:00:00', 'TongGuo', '', TIMESTAMP '2025-01-25 15:45:00', TIMESTAMP '2025-01-25 15:45:00', null, '基于考勤记录自动生成', 42.0, 630.00, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202502001', '84be3e3789866b57e024851de83f7e62', '2025', '2025-02', TIMESTAMP '2025-02-01 12:00:00', TIMESTAMP '2025-02-28 12:00:00', 'TongGuo', '', TIMESTAMP '2025-02-25 14:20:00', TIMESTAMP '2025-02-25 14:20:00', null, '基于考勤记录自动生成', 38.5, 577.50, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202503001', '84be3e3789866b57e024851de83f7e62', '2025', '2025-03', TIMESTAMP '2025-03-01 12:00:00', TIMESTAMP '2025-03-31 12:00:00', 'TongGuo', '', TIMESTAMP '2025-03-25 16:15:00', TIMESTAMP '2025-03-25 16:15:00', null, '基于考勤记录自动生成', 46.0, 690.00, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202504001', '84be3e3789866b57e024851de83f7e62', '2025', '2025-04', TIMESTAMP '2025-04-01 12:00:00', TIMESTAMP '2025-04-30 12:00:00', 'TongGuo', '', TIMESTAMP '2025-04-25 15:25:00', TIMESTAMP '2025-04-25 15:25:00', null, '基于考勤记录自动生成', 49.5, 742.50, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202505001', '84be3e3789866b57e024851de83f7e62', '2025', '2025-05', TIMESTAMP '2025-05-01 12:00:00', TIMESTAMP '2025-05-31 12:00:00', 'TongGuo', '', TIMESTAMP '2025-05-25 14:50:00', TIMESTAMP '2025-05-25 14:50:00', null, '基于考勤记录自动生成', 51.0, 765.00, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),
('wzw202506001', '84be3e3789866b57e024851de83f7e62', '2025', '2025-06', TIMESTAMP '2025-06-01 12:00:00', TIMESTAMP '2025-06-30 12:00:00', 'TongGuo', '', TIMESTAMP '2025-06-25 16:40:00', TIMESTAMP '2025-06-25 16:40:00', null, '基于考勤记录自动生成', 47.5, 712.50, 'admin', 'bc5afe28536dddbb9ae7ef7da50076b5', '报酬申报-图书馆小助理', null),