-- 报酬申请
INSERT INTO SYT_QGZX_REMUNERATION_APPLY (ID, JOB_ID, XNXQ, SBNY, START_DATE, END_DATE, SPZT, WORKFLOW_ID, SQSJ, CREATE_TIME, UPDATE_TIME, SBSM, TOTAL_HOURS, TOTAL_AMOUNT, XGH, ROLE_ID, XXMC, SFTS) VALUES ('7bc339ad95c852738b8385a64b73eb63', '0ce3616d6747ea9bc6502a2495a78fbb', '2025', '2025-07', TIMESTAMP '2025-07-01 12:00:00', TIMESTAMP '2025-07-31 12:00:00', 'TongGuo', '6012140939765841a57ba3865486e619', TIMESTAMP '2025-07-21 15:54:28.606871', TIMESTAMP '2025-07-21 15:54:28.606875', null, '基于考勤记录自动生成', 0.20, 3.00, 'xy001', '30a640ded7ebc5beb8fce3fb04ec6443', '报酬申报-图书馆小助理', null);

--报酬明细
INSERT INTO SYT_QGZX_REMUNERATION_DETAIL (ID, REMUNERATION_APPLY_ID, STUDENT_APPLY_ID, XGH, HOURS_SOURCE_TYPE, WORK_HOURS, HOURLY_RATE, REMUNERATION_AMOUNT, REMARK, CREATE_TIME, UPDATE_TIME) VALUES ('631beafd6c2032963a50001d593210e7', '7bc339ad95c852738b8385a64b73eb63', '1099dcc2a59af999746966a493def441', 'xh006', 'AUTO_ATTENDANCE', 0.20, 15.00, 3.00, '基于考勤记录自动计算', TIMESTAMP '2025-07-21 15:54:28.859076', null);


--学生
INSERT INTO SYT_XG_2023.SYT_USER_INFO (XGH) VALUES ('202500001');
INSERT INTO SYT_XG_2023.SYT_USER_INFO (XGH) VALUES ('202500002');
INSERT INTO SYT_XG_2023.SYT_USER_INFO (XGH) VALUES ('202500003');
INSERT INTO SYT_XG_2023.SYT_USER_INFO (XGH) VALUES ('202500004');
INSERT INTO SYT_XG_2023.SYT_USER_INFO (XGH) VALUES ('202500006');
